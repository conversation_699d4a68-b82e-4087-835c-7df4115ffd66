# **Language Model Request Lifecycle**

  

This document outlines the generalized lifecycle for handling requests to large language models (LLMs), including reasoning control, middleware handling, and response normalization. It is designed to work across various model providers (e.g. local, browser-based, or cloud-based models).

----------

## **1. Request Preparation**

-   The application obtains a model instance via a unified interface.
    
-   A provider-specific adapter is instantiated internally to bridge the unified interface with the underlying model implementation.
    
-   The language model is initialized with standardized configurations and capabilities.
    

----------

## **2. Request Body Transformation (Middleware Layer)**

-   A middleware function intercepts the request body before it is sent to the model backend.
    
-   It allows reasoning to be toggled based on user settings or application logic.
    

```
bodyTransformer: (body) => {
  if (options.reasoning) return body;

  // Disable reasoning by injecting a flag into the model request body
  return JSON.stringify({
    ...JSON.parse(body),
    think: false
  });
}
```

-   This design enables runtime control over whether the model should perform and return intermediate reasoning steps.
    

----------

## **3. Model Invocation**

-   Based on the desired output mode, the request is dispatched as either:
    
    -   A **streaming** request that returns tokens incrementally, or
        
    -   A **non-streaming** request that returns the entire output at once.
        
    
-   Request payload structure includes:
    

```
{
  model: modelId,
  messages: [...contextual prompts],
  options: { ...parameters such as temperature, top_k, etc. }
}
```

----------

## **4. Response Handling**

-   The response object typically contains:
    

```
{
  message: {
    content: string,          // Final model output
    thinking?: string,        // Optional reasoning trace
    tool_calls?: [...]        // Optional tool function invocations
  },
  ...metadata
}
```

-   The response is then:
    
    -   **Normalized**: extract reasoning (thinking) into a dedicated field (e.g. reasoning)
        
    -   **Analyzed**: map finish reasons and compute token usage
        
    -   **Validated**: optionally checked against expected output schema
        
    

----------

## **5. Lifecycle Summary**

```
graph TD
  A[User Request] --> B[Get Model Instance]
  B --> C[Instantiate Provider Adapter]
  C --> D[Transform Request Body (Middleware)]
  D --> E[Send Request to Model]
  E --> F[Parse Model Response]
  F --> G[Extract Output & Reasoning]
  G --> H[Return Final Result]
```

----------

## **✅ Key Features of This Design**

-   **Reasoning Control**: Toggle whether the model returns internal reasoning steps.
    
-   **Middleware Abstraction**: Centralized logic for modifying request behavior.
    
-   **Provider-Agnostic**: Compatible with multiple model backends.
    
-   **Output Structuring**: Separates intermediate reasoning from final answers.
    
-   **Error Resilience**: Flexible handling of optional fields and output formats.
